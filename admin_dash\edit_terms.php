<?php
// Simple Admin Panel Template

include("includes/header.php");
include("database/config.php");

// ✅ Update logic
$success = "";
if (isset($_POST['update'])) {
    $content = mysqli_real_escape_string($conn, $_POST['content']);
    $query = "UPDATE terms_conditions SET content='$content' WHERE id=1";
    if (mysqli_query($conn, $query)) {
        $success = "✅ नियमावली सफलतापूर्वक अपडेट हो गई।";
    } else {
        $success = "❌ अपडेट करते समय त्रुटि हुई!";
    }
}

// ✅ Fetch content
$result = mysqli_query($conn, "SELECT content FROM terms_conditions WHERE id=1");
$row = mysqli_fetch_assoc($result);
?>

<!DOCTYPE html>
<html lang="hi">

<head>
    <meta charset="UTF-8">
    <title>नियमावली संपादन</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- CKEditor -->
    <script src="https://cdn.ckeditor.com/4.22.1/standard/ckeditor.js"></script>
    <!-- Custom Styling -->
    <style>
        body {
            background-color: #f1f4f9;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        h2 {
            color: #2c3e50;
        }
        .btn-success {
            padding: 10px 30px;
            font-size: 18px;
        }
        .alert {
            font-size: 16px;
        }
        textarea {
            resize: none;
        }
    </style>
</head>

<body>
    <div class="container my-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card p-4">
                    <h2 class="text-center mb-4">✏️ नियमावली संपादन</h2>

                    <?php if ($success): ?>
                        <div class="alert alert-info text-center"><?= $success ?></div>
                    <?php endif; ?>

                    <form method="POST">
                        <textarea name="content" id="content" rows="15"><?php echo htmlspecialchars($row['content']); ?></textarea>
                        <script>
                            CKEDITOR.replace('content');
                        </script>

                        <div class="text-center mt-4">
                            <button type="submit" name="update" class="btn btn-success">💾 अपडेट करें</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

<?php include("includes/footer.php"); ?>
</body>
</html>
