<?php
session_start();
include("database/config.php");

if (isset($_POST['login'])) {
    $username = $_POST['username'];
    $password = md5($_POST['password']);
    $sql = "SELECT * FROM login WHERE username = '$username' AND password = '$password'";
    $result = mysqli_query($conn, $sql);
    $total = mysqli_num_rows($result);

    if ($total > 0) {
        $admin = mysqli_fetch_assoc($result);
        $_SESSION['admin_id'] = $admin['id'];
        $_SESSION['username'] = $admin['username'];
        echo "<script>alert('Successfully Login!!')</script>";
        header("location: index.php");
    } else {
        echo "<script>alert('Login Failed!!')</script>";
    }
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Login</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />

    <style>
        :root {
            --saffron: #FF9933;
            --saffron-dark: #E67E22;
            --shiva-blue: #3a5a78;
            --shiva-blue-dark: #2c3e50;
        }

        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .login-container {
            max-width: 450px;
            margin: 5rem auto;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            overflow: hidden;
        }

        .login-header {
            background: linear-gradient(135deg, var(--shiva-blue), var(--shiva-blue-dark));
            color: white;
            padding: 1.5rem;
            text-align: center;
        }

        .login-header img {
            width: 70px;
            margin-bottom: 1rem;
        }

        .login-body {
            background: white;
            padding: 2rem;
        }

        .form-control {
            padding: 12px 15px;
            border-radius: 5px;
        }

        .btn-login {
            background-color: var(--saffron);
            border: none;
            padding: 12px;
            font-weight: 600;
            transition: all 0.3s;
        }

        .btn-login:hover {
            background-color: var(--saffron-dark);
        }

        .input-group-text {
            background-color: #e9ecef;
            border-right: none;
        }

        .form-floating label {
            padding: 1rem 1.75rem;
        }

        .form-floating>.form-control:focus~label {
            color: var(--shiva-blue);
        }

        .password-toggle {
            cursor: pointer;
            background-color: #e9ecef;
            border-left: none;
        }

        .forgot-link {
            color: var(--shiva-blue);
            text-decoration: none;
        }

        .forgot-link:hover {
            text-decoration: underline;
        }

        .admin-icon {
            font-size: 1.2rem;
            margin-right: 8px;
        }
    </style>


</head>

<body>
    <div class="container">
        <div class="login-container">
            <div class="login-header">
                <img src="images/om.png" alt="Shiva Icon">
                <h3><i class="fas fa-user-shield admin-icon"></i> संस्थान प्रशासन</h3>
                <p class="mb-0">Rudra Narmadeshwar Seva Samiti Admin Panel</p>
            </div>
            <div class="login-body">
                <!--   Error Message Alert -->
                <div class="alert alert-danger d-none" id="loginError">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <span id="errorText">Invalid username or password</span>
                </div>

                <form id="adminLoginForm" method="POST" action="admin-login.php" autocomplete="off">
                    <div class="form-floating mb-3">
                        <input type="text" name="username" class="form-control" id="username" placeholder="Username" required>
                        <label for="username"><i class="fas fa-user me-2"></i> उपयोगकर्ता नाम</label>
                    </div>


                    <div class="form-floating mb-3">
                        <div class="input-group">
                            <input type="password" name="password" class="form-control" id="password" placeholder="Password" required>
                            <span class="input-group-text password-toggle" id="togglePassword">
                                <i class="fas fa-eye"></i>
                            </span>
                            <label for="password"><i class="fas fa-key me-2"></i> पासवर्ड</label>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="rememberMe">
                            <label class="form-check-label" for="rememberMe">मुझे याद रखें</label>
                        </div>
                        <a href="#" class="forgot-link">पासवर्ड भूल गए?</a>
                    </div>
                    <button type="submit" name="login" class="btn btn-login btn-primary w-100">
                        <i class="fas fa-sign-in-alt me-2"></i> लॉग इन
                    </button>
                </form>
            </div>

            <div class="login-footer text-center p-3 bg-light">
                <p class="small mb-0">© 2023 रूद्र नर्मदेश्वर सेवा समिति. सर्वाधिकार सुरक्षित</p>
            </div>
        </div>
    </div>


</body>

</html>