<?php
session_start();
// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
  // Redirect to login page if not logged in
  header("Location: admin-login.php");
  exit();
}

$admin_id = $_SESSION['admin_id'];
include("database/config.php");
$query = "SELECT * FROM login where id='$admin_id'";
$data = mysqli_query($conn, $query);
$result = mysqli_fetch_assoc($data);
?>


<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>NGO Admin Panel</title>
  
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- bootstrap -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.13.1/font/bootstrap-icons.min.css">
  <link href="https://fonts.googleapis.com/css?family=Poppins:400,700&display=swap" rel="stylesheet">
  <style>
    :root {
      --light: #f4f4f4;
      --shiva-blue: #003366;
      --saffron: #ff9933;
      --white: #ffffff;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      min-height: 100vh;
      background: var(--light);
      font-family: 'Poppins', sans-serif;
    }

    .sidebar {
      position: fixed;
      top: 0;
      left: 0;
      height: 100vh;
      width: 220px;
      background: var(--shiva-blue);
      color: var(--white);
      display: flex;
      flex-direction: column;
      box-shadow: 2px 0 15px rgba(0, 0, 0, 0.07);
      z-index: 10;
      transition: all 0.3s ease;
    }

    .sidebar-header {
      font-size: 1.5rem;
      font-weight: 700;
      padding: 32px 24px 24px 24px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    }

    .sidebar-menu {
      flex: 1;
      padding: 24px 0;
    }

    .sidebar-menu a {
      display: block;
      color: var(--white);
      text-decoration: none;
      padding: 14px 32px;
      font-size: 1.1rem;
      border-radius: 4px 0 0 4px;
      margin-bottom: 8px;
      transition: background 0.2s;
    }

    .sidebar-menu a.active,
    .sidebar-menu a:hover {
      background: var(--saffron);
      color: var(--shiva-blue);
    }

    .main-content {
      margin-left: 220px;
      padding: 40px 32px;
      transition: margin-left 0.3s ease;
    }

    .dashboard-header {
      font-size: 2.2rem;
      font-weight: 700;
      color: var(--shiva-blue);
      margin-bottom: 32px;
    }

    .card-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
      gap: 32px;
    }

    .dashboard-card {
      background: rgba(255, 255, 255, 0.92);
      border-radius: 18px;
      box-shadow: 0 4px 32px 0 rgba(0, 0, 0, 0.18);
      padding: 32px 24px 28px 24px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      transition: box-shadow 0.2s, transform 0.2s;
      position: relative;
      overflow: hidden;
      min-height: 98px;
    }

    .dashboard-card:hover {
      box-shadow: 0 8px 40px 0 rgba(0, 0, 0, 0.25);
      transform: translateY(-4px) scale(1.03);
    }

    .card-title {
      font-size: 1.4rem;
      font-weight: 700;
      color: black;
      margin-bottom: 10px;
      z-index: 2;
    }

    .card-desc {
      font-size: 1.05rem;
      color: #000;
      margin-bottom: 18px;
      z-index: 2;
    }

    .dashboard-card.red::before,
    .dashboard-card.orange::before,
    .dashboard-card.green::before,
    .dashboard-card.yellow::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 10px;
      border-radius: 18px 0 0 18px;
      z-index: 1;
    }

    .dashboard-card.red::before {
      background: linear-gradient(135deg, #ff3c3c, #ff7b47);
      box-shadow: 0 0 32px 8px #ff3c3c44;
    }

    .dashboard-card.orange::before {
      background: linear-gradient(135deg, #ff9900, #ffb347);
      box-shadow: 0 0 32px 8px #ff990044;
    }

    .dashboard-card.green::before {
      background: linear-gradient(135deg, #00ff6a, #1de982);
      box-shadow: 0 0 32px 8px #00ff6a44;
    }

    .dashboard-card.yellow::before {
      background: linear-gradient(135deg, #ffe259, #ffa751);
      box-shadow: 0 0 32px 8px #ffe25944;
    }

    .dashboard-card .card-title,
    .dashboard-card .card-desc {
      position: relative;
    }

    /* Table Styles */
    .table-container {
      margin-top: 48px;
      background: var(--white);
      border-radius: 12px;
      box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
      overflow-x: auto;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      min-width: 600px;
    }

    thead {
      background: var(--shiva-blue);
      color: var(--white);
    }

    th,
    td {
      padding: 16px 20px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }

    tbody tr:hover {
      background: #f9f9f9;
    }

    /* Hamburger Button */
    .hamburger {
      display: none;
      position: absolute;
      top: 16px;
      right: 16px;
      cursor: pointer;
      font-size: 1.8rem;
      color: var(--white);
    }

    @media (max-width: 768px) {
      .sidebar {
        width: 100%;
        height: auto;
        flex-direction: column;
        overflow: scroll;
      }

      .sidebar-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 24px;
      }

      .hamburger {
        display: block;
      }

      .sidebar-menu {
        display: none;
        flex-direction: column;
        background: var(--shiva-blue);
      }

      .sidebar-menu.show {
        display: flex;
      }

      .sidebar-menu a {
        padding: 12px 24px;
        font-size: 1rem;
      }

      .main-content {
        margin-left: 0;
        padding: 24px 16px;
      }

      .card-grid {
        grid-template-columns: 1fr;
        gap: 16px;
      }

      .dashboard-card {
        padding: 24px 16px;
      }
    }

    .logo {
      width: 50px;
      height: 50px;
      margin-top: 0;
      margin-left: 40px;
      margin-bottom: 5px;
    }
  </style>
</head>

<body>
  <div class="sidebar">
    <div class="sidebar-header">
      <img src="images/om.png" alt="LOGO" class="logo">
      <h4> <?php echo "NGO " . $result['username'] . " Dashboard"; ?></h4>
      <div class="hamburger" id="hamburger">&#9776;</div>
    </div>
    <div class="mb-3">
      <input type="hidden" name="id" class="form-control" value="" />
    </div>
    <div class="sidebar-menu" id="sidebarMenu">
      <ul style="list-style: none; padding: 0;">
        <li class="mb-3 d-flex align-items-center gap-3">
          <a href="index.php" class="active">
            <i class="fas fa-home"></i>
            <span>डैशबोर्ड</span>
          </a>
        </li>

        <li class="mb-3 d-flex align-items-center gap-3">
          <a href="terms.php">
            <i class="fas fa-file-contract"></i>
            <span>नियमावली</span>
          </a>
        </li>

        <li class="mb-3 d-flex align-items-center gap-2">
          <a href="#">
            <i class="fas fa-users"></i>
            <span>सदस्य सूची</span>
          </a>
        </li>

        <li class="mb-3 d-flex align-items-center gap-2">
          <a href="payment.php">
            <i class="fas fa-chart-line"></i>
            <span>आर्थिक रिपोर्ट</span>
          </a>
        </li>

        <li class="mb-3 d-flex align-items-center gap-2">
          <a href="contact.php">
            <i class="fas fa-envelope"></i>
            <span>संपर्क</span>
          </a>
        </li>

        <li class="mb-3 d-flex align-items-center gap-2">
          <a href="admin_profile.php">
            <i class="fas fa-cog"></i>
            <span>सेटिंग्स</span>
          </a>
        </li>

        <li class="d-flex align-items-center gap-2">
          <a href="logout.php">
            <i class="fas fa-sign-out-alt"></i>
            <span>लॉग आउट</span>
          </a>
        </li>
      </ul>
    </div>
  </div>

  <div class="main-content">
    <div class="dashboard-header">Dashboard
      <hr>
    </div>