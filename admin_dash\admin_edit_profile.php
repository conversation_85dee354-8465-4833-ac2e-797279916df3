<?php

include("includes/header.php");
$user = $_SESSION["username"];
if ($user == true) {
} else {
  header("location:admin-login.php");
}
include("database/config.php");

$id =  $_GET['id'];
// echo $id;
$query = "SELECT * FROM login where id = '$id'";
$data = mysqli_query($conn, $query);
$total = mysqli_num_rows($data);
$result = mysqli_fetch_assoc($data);

if (isset($_POST["update"])) {


  $username = $_POST['username'];  //echo $username; echo '<br>';
  //$mobile = $_POST['mobile']; //echo $mobile; echo '<br>';    
  $email = $_POST['email'];  //echo $email; echo '<br>';
  //$password = $_POST['password']; //echo $password; echo "<br>";
  // $password = password_hash($_POST['password'], PASSWORD_DEFAULT);


  $query = "UPDATE login SET username ='$username',email='$email',password='$password' WHERE id='$id'";

  $data = mysqli_query($conn, $query);

  if ($data) {
    echo "<script>alert('Your record sucessfully updated!')</script>";
  } else {
    echo "<script>alert('Failed to Update!')</script>";
  }
}
// error_reporting(0);
?>




<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Update Admin</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link rel="stylesheet" href="style.css">
</head>

<body class="bg-light">
  <div class="container py-5">
    <div class="col-lg-6 mx-auto">
      <div class="card shadow p-4">
        <h4 class="mb-4 text-center">Update Admin Details</h4>
        <form method="POST" action="#" enctype="multipart/form-data">
          <div class="mb-3">
            <input type="hidden" name="id" class="form-control" value="" />
          </div>

          <div class="mb-3">
            <label for="username" class="form-label">Username</label>
            <input type="text" name="username" value="<?php echo $result['username']; ?>" class="form-control" placeholder="Enter username" required>
          </div>
          <!-- <div class="mb-3">
            <label for="username" class="form-label">Mobile No.</label>
            <input type="number" name="mobile" value="<?php //echo $result['mobile']; 
                                                      ?>" class="form-control" placeholder="Enter Mobile" required>
          </div> -->
          <div class="mb-3">
            <label for="email" class="form-label">Email</label>
            <input type="email" name="email" value="<?php echo $result['email']; ?>" class="form-control" placeholder="Enter email" required>
          </div>
          <!-- <div class="mb-3">
            <label for="password" class="form-label">Password</label>
            <input type="password" name="password" value="<?php echo $result['password']; ?>" class="form-control" placeholder="Enter password" required>
          </div> -->
          <button type="submit" name="update" class="btn btn-success w-100">Update</button>
        </form>
      </div>
    </div>
  </div>

  <?php include("includes/footer.php"); ?>