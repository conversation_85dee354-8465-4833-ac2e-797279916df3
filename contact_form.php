<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if form is submitted via AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['username'])) {

    // Step 1: Connect to DB
    $conn = new mysqli("localhost", "root", "", "ngo");

    // Step 2: Check connection
    if ($conn->connect_error) {
        echo "❌ DB connection failed: " . $conn->connect_error;
        exit();
    }

    // Step 3: Read POST values
    $username = $_POST['username'];
    $email    = $_POST['email'];
    $mobile   = $_POST['mobile'];
    $subject  = $_POST['subject'];
    $message  = $_POST['message'];
   

    // Step 4: Build & run query
    $sql = "INSERT INTO contact (username, email, mobile, subject, message) VALUES ('$username', '$email', '$mobile', '$subject', '$message')";

    if ($conn->query($sql)) {
        echo "success";
    } else {
        echo "❌ MySQL Error: " . $conn->error; // <--- Show exact error
    }

    exit();
}
?>

  