<?php
include("includes/header.php");
include("database/config.php");

$result = mysqli_query($conn, "SELECT * FROM contact ORDER BY id DESC");
?>

<!DOCTYPE html>
<html lang="hi">

<head>
    <meta charset="UTF-8">
    <title>प्राप्त संदेश</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- Bootstrap CSS -->
    <!-- <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css" rel="stylesheet"> -->
    <style>
        body {
            background-color: #f8f9fa;
        }

        .message-container {
            background-color: #fff;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 0 12px rgba(0, 0, 0, 0.1);
        }

        h2 {
            text-align: center;
            font-weight: 600;
            color: #343a40;
            font-family: 'Mukt<PERSON>', sans-serif;
            font-weight: bold;
            margin-bottom: 15px;
        }

        hr.custom-hr {
            width: 200px;
            height: 2.5px;
            background-color: #ccc;
            border: none;
            margin: 20px auto;
            border-radius: 5px;
            opacity: 0.9;
        }

        .table thead {
            background-color: #007bff;
            color: white;
        }

        .table td,
        .table th {
            vertical-align: middle;
            font-size: 14px;
        }

        .alert-success {
            font-weight: 500;
        }
    </style>
</head>

<body>
    <div class="container py-5">
        <div class="message-container">
            <h2 class="text-primary">प्राप्त संदेश</h2>
            <hr class="custom-hr">

            <?php if (isset($_GET['added'])): ?>
                <div class="alert alert-success text-center">✅ संदेश सफलतापूर्वक जोड़ा गया!</div>
            <?php endif; ?>

            <div class="table-responsive">
                <table class="table table-striped table-bordered">
                    <thead>
                        <tr>

                            <th>नाम</th>
                            <th>ईमेल</th>
                            <th>मोबाइल</th>
                            <th>विषय</th>
                            <th>संदेश</th>
                            <th>तारीख और समय</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while ($row = mysqli_fetch_assoc($result)) { ?>
                            <tr>
                                <td><?= htmlspecialchars($row['username']) ?></td>
                                <td><?= htmlspecialchars($row['email']) ?></td>
                                <td><?= htmlspecialchars($row['mobile']) ?></td>
                                <td><?= htmlspecialchars($row['subject']) ?></td>
                                <td><?= nl2br(htmlspecialchars($row['message'])) ?></td>
                                <td><?= htmlspecialchars(date("d/M/Y h:iA", strtotime($row['submitted_at'])))  ?></td>
                             
                            </tr>
                        <?php } ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <?php include("includes/footer.php"); ?>
</body>

</html>