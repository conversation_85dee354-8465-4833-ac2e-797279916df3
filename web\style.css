  :root {
    --saffron: #FF9933;
    --saffron-dark: #E67E22;
    --shiva-blue: #3a5a78;
    --shiva-blue-dark: #2c3e50;
    --white: #ffffff;
    --light: #f8f9fa;
    --dark: #212529;
    --success: #27ae60;
  }

  body {
    font-family: 'Poppins', sans-serif;
    color: var(--dark);
    background-color: var(--light);
  }

  .hindi {
    font-family: 'Hind', sans-serif;
  }

  .navbar {
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    background-color: var(--white) !important;
  }

  .navbar-brand {
    font-weight: 700;
    color: var(--shiva-blue) !important;
  }

  .bg-saffron-gradient {
    background: linear-gradient(135deg, var(--saffron), var(--saffron-dark));
  }

  .btn-primary {
    background-color: var(--shiva-blue);
    border-color: var(--shiva-blue);
    color: white;
  }

  .btn-primary:hover {
    background-color: var(--shiva-blue-dark);
    border-color: var(--shiva-blue-dark);
    color: white;
  }

  .btn-accent{
    background-color: var(--saffron);
    border-color: var(--saffron);
   
  }
  .btn-accent a{
    color: white;
    text-decoration: none;
  }

  .btn-accent:hover {
    background-color: var(--saffron-dark);
    border-color: var(--saffron-dark);
    color: white;
  }

  .hero-section {
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('images/23541.jpg');
    background-size: cover;
    background-position: center;
    color: white;
    padding: 180px 0;
    position: relative;
  }

  .shiva-icon {
    font-size: 3rem;
    color: var(--saffron);
    margin-bottom: 1rem;
  }

  .mission-card {
    background-color: var(--white);
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
    border-top: 5px solid var(--saffron);
    height: 100%;
  }

  .linga-count {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--saffron);
    line-height: 1;
  }

  .donation-form {
    background-color: var(--white);
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    border-top: 5px solid var(--shiva-blue);
  }

  .testimonial-card {
    border-left: 4px solid var(--saffron);
    border-radius: 0;
  }

  .contact-info {
    background-color: var(--white);
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  }

  .section-heading {
    position: relative;
    display: inline-block;
    margin-bottom: 40px;
  }

  .section-heading::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, var(--shiva-blue), var(--saffron));
  }

  @media (max-width: 768px) {
    .hero-section {
      padding: 120px 0;
    }
  }

  /* Animation */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate {
    animation: fadeInUp 0.5s ease forwards;
  }

  /* Logo styling */
  .logo-text {
    display: flex;
    align-items: center;
  }

  .logo-icon {
    font-size: 1.8rem;
    margin-right: 10px;
    color: var(--saffron);
  }

  .mission-highlight-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
  }

  .mission-highlight-card:hover {
    transform: translateY(-5px);
  }

  .icon-circle {
    width: 60px;
    height: 60px;
    margin: 0 auto;
    background: rgba(58, 90, 120, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--shiva-blue);
  }

  .lingam-grid {
    background: #f8f5f0;
    padding: 30px;
    border-radius: 10px;
    border: 1px dashed var(--saffron);
  }

  .text-ornate {
    color: var(--saffron);
    opacity: 0.7;
  }

  .mission-cta {
    background: linear-gradient(135deg, rgba(58, 90, 120, 0.05), rgba(230, 126, 34, 0.05));
    border: 1px solid rgba(230, 126, 34, 0.2);
  }

  .badge.bg-primary {
    background-color: var(--saffron) !important;
  }

  .contact-card {
    background: rgba(255, 153, 51, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(255, 153, 51, 0.2);
  }

  .contact-form-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    border-top: 3px solid var(--saffron);
  }

  .spiritual-message {
    background: rgba(58, 90, 120, 0.05);
    border-radius: 50px;
    font-style: italic;
  }

  .form-floating label {
    font-family: 'Hind', sans-serif;
  }

  .text-saffron {
    color: var(--saffron);
  }

  @media (max-width: 767px) {
    .spiritual-message {
      border-radius: 20px;
    }
  }



  .campaign-icon i {
    color: var(--saffron);
    background: rgba(255, 153, 51, 0.1);
    padding: 20px;
    border-radius: 50%;
  }

  .campaign-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    height: 100%;
    transition: transform 0.3s ease;
    position: relative;
  }

  .campaign-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }

  .campaign-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: var(--saffron);
    color: white;
    padding: 3px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    z-index: 1;
  }

  .campaign-img {
    width: 100%;
    height: 180px;
    object-fit: cover;
    border-bottom: 3px solid var(--saffron);
  }

  .campaign-body {
    padding: 20px;
  }

  .campaign-desc {
    color: var(--text-light);
    font-size: 0.9rem;
    min-height: 40px;
  }

  .campaign-features {
    margin: 10px 0;
  }

  .progress {
    height: 8px;
    border-radius: 4px;
  }

  .progress-bar {
    background-color: var(--saffron);
  }

  .quick-donation-option {
    background: white;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
  }

  .quick-donation-option:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  @media (max-width: 767px) {
    .campaign-img {
      height: 150px;
    }
  }



  .donation-icon img {
    filter: hue-rotate(10deg) brightness(0.9);
  }

  .donation-options-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    border-top: 3px solid var(--saffron);
  }

  .donation-form-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    border-top: 3px solid var(--shiva-blue);
  }

  .donation-tier {
    background: white;
    border-radius: 8px;
    border: 1px solid #eee;
    transition: all 0.3s ease;
    height: 100%;
  }

  .donation-tier:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .active-tier {
    border: 2px solid var(--saffron);
    background: rgba(255, 153, 51, 0.05);
  }

  .tier-header {
    position: relative;
  }

  .tier-badge {
    position: absolute;
    top: -10px;
    right: -10px;
    background: var(--saffron);
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.7rem;
  }

  .tier-icon {
    color: var(--saffron);
    margin: 0 2px;
  }

  .tier-amount {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--shiva-blue);
    margin: 10px 0;
  }

  .custom-amount {
    background: rgba(58, 90, 120, 0.05);
    border-radius: 8px;
  }

  .selected-amount-display {
    background: var(--shiva-blue);
    color: white;
    display: inline-block;
    padding: 5px 15px;
    border-radius: 20px;
    margin-top: 10px;
  }

  .payment-methods {
    background: #f9f9f9;
    padding: 15px;
    border-radius: 8px;
  }

  .donation-benefits {
    background: rgba(58, 90, 120, 0.05);
    border-radius: 10px;
  }

  .benefit-item {
    background: white;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
  }

  .benefit-icon {
    font-size: 2rem;
    color: var(--saffron);
    margin-bottom: 10px;
  }


  .gallery-card {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
  }

  .gallery-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  }

  .gallery-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: all 0.3s ease;
  }

  .gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(58, 90, 120, 0.8);
    color: white;
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .gallery-card:hover .gallery-overlay {
    opacity: 1;
  }

  .overlay-content {
    text-align: center;
    padding: 15px;
  }

  .overlay-icon {
    font-size: 1.5rem;
    margin-top: 10px;
    color: var(--saffron);
  }

  .filter-button {
    margin: 2px;
  }

  .btn-group-toggle .active {
    background-color: var(--saffron);
    color: white;
  }

  @media (max-width: 767px) {
    .gallery-card img {
      height: 150px;
    }
  }


