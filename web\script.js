
// Handle tier selection
document.querySelectorAll('.select-tier').forEach(button => {
    button.addEventListener('click', function (e) {
        e.preventDefault();

        // Remove active class from all tiers
        document.querySelectorAll('.donation-tier').forEach(tier => {
            tier.classList.remove('active-tier');
            tier.querySelector('button').classList.remove('btn-accent');
            tier.querySelector('button').classList.add('btn-outline-accent');
            tier.querySelector('button').textContent = 'चुनें';
        });

        // Add active class to selected tier
        const tier = this.closest('.donation-tier');
        tier.classList.add('active-tier');
        this.classList.remove('btn-outline-accent');
        this.classList.add('btn-accent');
        this.textContent = 'चयनित';

        // Update displayed amount
        const amount = tier.getAttribute('data-amount');
        document.getElementById('displayAmount').textContent = `₹${parseInt(amount).toLocaleString('en-IN')}`;
    });
});

// Handle custom amount selection
document.getElementById('selectCustom').addEventListener('click', function (e) {
    e.preventDefault();
    const amount = document.getElementById('customAmount').value;
    if (amount && amount > 0) {
        document.getElementById('displayAmount').textContent = `₹${parseInt(amount).toLocaleString('en-IN')}`;

        // Update active tier
        document.querySelectorAll('.donation-tier').forEach(tier => {
            tier.classList.remove('active-tier');
            tier.querySelector('button').classList.remove('btn-accent');
            tier.querySelector('button').classList.add('btn-outline-accent');
            tier.querySelector('button').textContent = 'चुनें';
        });
    }
});

// Form submission
// Initialize lightbox functionality
document.querySelectorAll('.gallery-card').forEach(card => {
    card.addEventListener('click', function () {
        const imgSrc = this.querySelector('img').src;
        const title = this.querySelector('h5').textContent;
        const desc = this.querySelector('p').textContent;

        document.getElementById('modalImage').src = imgSrc;
        document.getElementById('modalTitle').textContent = title;
        document.getElementById('modalDesc').textContent = desc;

        var modal = new bootstrap.Modal(document.getElementById('galleryModal'));
        modal.show();
    });
});




// < !--Gallery Enhancement Script-- >

// View More Button Functionality
document.querySelector('.btn-accent').addEventListener('click', function () {
    // In a real implementation, this would load more gallery items via AJAX
    alert('धन्यवाद! हम जल्द ही और छवियाँ प्रदर्शित करेंगे।\n\nThank you! More images will be loaded soon.');
});

// Social Sharing Functionality
function shareImage(imgSrc) {
    // Implementation for social media sharing
    console.log('Sharing image:', imgSrc);
    alert('इस छवि को साझा करने के लिए धन्यवाद!\n\nThank you for sharing this image!');
}


