-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jul 27, 2025 at 07:57 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `ngo`
--

-- --------------------------------------------------------

--
-- Table structure for table `contact`
--

CREATE TABLE `contact` (
  `id` int(11) NOT NULL,
  `username` varchar(100) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `mobile` varchar(20) DEFAULT NULL,
  `subject` varchar(100) DEFAULT NULL,
  `message` text DEFAULT NULL,
  `submitted_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `contact`
--

INSERT INTO `contact` (`id`, `username`, `email`, `mobile`, `subject`, `message`, `submitted_at`) VALUES
(1, 'Shital', '<EMAIL>', '8855221144', 'donation', 'Donation', '2025-07-22 19:45:25'),
(2, 'Meera Soni', '<EMAIL>', '8855221144', 'donation', 'Donate', '0000-00-00 00:00:00');

-- --------------------------------------------------------

--
-- Table structure for table `donations`
--

CREATE TABLE `donations` (
  `id` int(11) NOT NULL,
  `name` varchar(100) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `mobile` varchar(20) DEFAULT NULL,
  `state` varchar(50) DEFAULT NULL,
  `city` varchar(50) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `purpose` varchar(100) DEFAULT NULL,
  `amount` int(11) DEFAULT NULL,
  `donation_type` varchar(100) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `donations`
--

INSERT INTO `donations` (`id`, `name`, `email`, `mobile`, `state`, `city`, `address`, `purpose`, `amount`, `donation_type`, `created_at`) VALUES
(1, 'Shital', '<EMAIL>', '8855221144', 'Chhattisgarh', 'Durg', 'Durg', 'annadan', 5000, 'एक शिवलिंग', '2025-07-22 19:44:27'),
(2, 'Meera Soni', '<EMAIL>', '8855221144', 'Gujarat', 'AMH', 'AMH', 'education', 100, 'अन्य राशि', '0000-00-00 00:00:00'),
(3, 'kunal', '<EMAIL>', '08319921729', 'Chhattisgarh', 'Durg', 'Behind Vivekanand School Near Gura Chora Shitla Nagar Durg C.G.', 'annadan', 200, 'अन्य राशि', '0000-00-00 00:00:00');

-- --------------------------------------------------------

--
-- Table structure for table `login`
--

CREATE TABLE `login` (
  `id` int(100) NOT NULL,
  `username` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `login`
--

INSERT INTO `login` (`id`, `username`, `email`, `password`) VALUES
(1, 'admin', '<EMAIL>', '4e7a98c7386ba6064271551680ab0afb');

-- --------------------------------------------------------

--
-- Table structure for table `terms_conditions`
--

CREATE TABLE `terms_conditions` (
  `id` int(11) NOT NULL,
  `content` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `terms_conditions`
--

INSERT INTO `terms_conditions` (`id`, `content`) VALUES
(1, '<p><strong>संस्था का नाम:</strong> रूद्र नर्मदेश्वर सेवा समिति</p>\r\n\r\n<p><strong>संस्था का प्रधान कार्यालय:</strong> द्वारा श्री युगल किशोर दुबे, श्री राधा कुंज, वार्ड नं. 57, नॅशनल हाइवे के पहले, बजरंग होटेल के पास, उरला दुर्ग, पि.को. 491001, जिला दुर्ग (छ.ग.)</p>\r\n\r\n<p><strong>संस्था का कार्यक्षेत्र:</strong> छत्तीसगढ़ राज्य</p>\r\n\r\n<p>संस्था के उद्देश्य:</p>\r\n\r\n<ol>\r\n	<li>लोगों के सर्वांगीण विकास के लिए कार्य करना।</li>\r\n	<li>प्राकृतिक आपदा, बाढ़, सूखा आदि से ग्रस्त लोगों की सहायता करना।</li>\r\n	<li>समय-समय पर भजन एवं सत्संगों का आयोजन करना।</li>\r\n	<li>लोगों का आध्यात्मिक, धार्मिक, सांस्कृतिक, बौद्धिक, मानसिक उत्थान करना।</li>\r\n	<li>संत समागम एवं सत्संग का आयोजन करना।</li>\r\n	<li>लोगों को आध्यात्म के बारे में जानकारी देना।</li>\r\n	<li>सामाजिक कल्याण के कार्य करना तथा समाज में व्याप्त कुरीतियों को दूर करना।</li>\r\n	<li>धार्मिक कर्मकांड करना, धार्मिक एवं चैरिटेबल संस्थाओं को सहयोग प्रदान करना।</li>\r\n	<li>लोगों के लिए योग कक्षा का संचालन तथा योग शिविरों का आयोजन करना।</li>\r\n	<li>बच्चों के लिए गुरुकुल का संचालन करना।</li>\r\n	<li>लोगों के लिए ग्रीष्म ऋतु में प्याऊ घर का संचालन करना।</li>\r\n	<li>धार्मिक रीति रिवाज के अनुसार धार्मिक सम्मेलनों का आयोजन करना।</li>\r\n	<li>वातावरण को स्वच्छ बनाए रखने के लिए वृक्षारोपण करना।</li>\r\n	<li>स्वास्थ्य एवं जागरूकता शिविर का आयोजन करना।</li>\r\n	<li>नशा मुक्ति हेतु कार्यशालाओं का आयोजन करना एवं लोगों को जागरूक करना।</li>\r\n	<li>लोगों को आत्मनिर्भर बनाने के लिए रोजगार उन्मुखी प्रशिक्षण देना।</li>\r\n	<li>शासन की योजनाओं को लोगों तक पहुंचाना एवं उनका संचालन करना।</li>\r\n	<li>शहरी एवं ग्रामीण क्षेत्रों में कंप्यूटर एवं तकनीकी शिक्षा उपलब्ध कराना।</li>\r\n	<li>छात्र-छात्राओं को तकनीकी, आधुनिक एवं स्वरोजगार शिक्षा उपलब्ध कराना।</li>\r\n	<li>प्रौढ़ शिक्षा एवं साक्षरता को बढ़ावा देना।</li>\r\n	<li>महिलाओं को स्वरोजगार प्रशिक्षण उपलब्ध कराना एवं महिला सशक्तिकरण को बढ़ावा देना।</li>\r\n	<li>हस्तशिल्प कला को मंच प्रदान करना, सेमिनार एवं प्रदर्शनी का आयोजन करना।</li>\r\n	<li>वृद्ध एवं निशक्तजनों की सहायता करना एवं वृद्धाश्रम का संचालन करना।</li>\r\n	<li>जल संरक्षण एवं संवर्धन हेतु कार्य करना।</li>\r\n	<li>गैर पारंपरिक ऊर्जा स्रोतों को बढ़ावा देना एवं योजनाओं में सहभागिता करना।</li>\r\n	<li>निर्धन कन्याओं के विवाह हेतु सामूहिक विवाह कार्यक्रम आयोजित करना।</li>\r\n	<li>बेहतर स्वास्थ्य सुविधाएं उपलब्ध कराना एवं स्वास्थ्य परीक्षण शिविरों का आयोजन करना।</li>\r\n	<li>घायल पशु-पक्षियों की सेवा करना।</li>\r\n</ol>\r\n\r\n<p>सदस्यता:</p>\r\n\r\n<p><strong>संस्था की सदस्यता श्रेणियाँ:</strong></p>\r\n\r\n<ol>\r\n	<li><strong>संरक्षक सदस्य:</strong> 25,000/- रु दान करने वाला संरक्षक सदस्य होगा।</li>\r\n	<li><strong>आजीवन सदस्य:</strong> 20,000/- रु दान देने वाला आजीवन सदस्य होगा।</li>\r\n	<li><strong>साधारण सदस्य:</strong> 500/- रु प्रतिमाह या 6,000/- रु प्रतिवर्ष देने वाला साधारण सदस्य होगा।</li>\r\n	<li><strong>सम्माननीय सदस्य:</strong> समिति किसी व्यक्ति को लिखित सहमति से सम्माननीय सदस्य बना सकती है।</li>\r\n</ol>\r\n\r\n<p>सदस्यता प्राप्ति, योग्यता, समाप्ति, निष्कासन:</p>\r\n\r\n<p>सदस्यता प्राप्ति के लिए लिखित आवेदन आवश्यक है। सदस्य की आयु 18 वर्ष से कम नहीं होनी चाहिए, भारतीय नागरिक हो एवं संस्था के नियमों का पालन करे। मृत्यु, पागलपन, बकाया चंदा न देना, त्यागपत्र देना या चरित्र दोष के कारण सदस्यता समाप्त हो सकती है। संस्था हितों के विरुद्ध कार्य करने पर निष्कासन हो सकता है।</p>\r\n\r\n<p>सदस्यता पंजी:</p>\r\n\r\n<p>प्रधान कार्यालय में सदस्यता पंजी रखा जाएगा जिसमें सभी विवरण नियमित रूप से दर्ज होंगे।</p>\r\n\r\n<p>संस्था की सभाएँ:</p>\r\n\r\n<ol>\r\n	<li><strong>साधारण सभा:</strong> वर्ष में एक बार अनिवार्य।</li>\r\n	<li><strong>प्रबंधकारिणी सभा:</strong> हर 3&nbsp;माह में एक बार अनिवार्य।</li>\r\n	<li><strong>विशेष साधारण सभा:</strong> आवश्यकता अनुसार बुलाई जाएगी।</li>\r\n</ol>\r\n\r\n<p>साधारण सभा के अधिकार:</p>\r\n\r\n<ul>\r\n	<li>वार्षिक प्रतिवेदन स्वीकृत करना।</li>\r\n	<li>स्थाई निधि की व्यवस्था करना।</li>\r\n	<li>लेखा परीक्षकों की नियुक्ति करना।</li>\r\n	<li>अन्य विषयों पर निर्णय लेना।</li>\r\n</ul>\r\n\r\n<p>प्रबंधकारिणी समिति:</p>\r\n\r\n<p>समिति में अध्यक्ष, उपाध्यक्ष, सचिव, कोषाध्यक्ष, संयुक्त सचिव एवं चार सदस्य होंगे। कार्यकाल 3 वर्ष का होगा।</p>\r\n\r\n<p>प्रबंधकारिणी समिति के अधिकार:</p>\r\n\r\n<p>संस्था के उद्देश्यों की पूर्ति करना, कर्मचारियों की नियुक्ति, आय-व्यय का लेखा प्रस्तुत करना, नियमावली बनाना, अनुशासन बनाए रखना आदि।</p>\r\n\r\n<p>पदाधिकारियों के अधिकार:</p>\r\n\r\n<p><strong>अध्यक्ष:</strong> सभी बैठकों की अध्यक्षता करेगा।<br />\r\n<strong>उपाध्यक्ष:</strong> अध्यक्ष अनुपस्थित हो तो अध्यक्ष का कार्य करेगा।<br />\r\n<strong>सचिव:</strong> बैठकों का आयोजन, लेखा तैयार करना, रखरखाव, अनियमितताओं की रिपोर्ट देना।<br />\r\n<strong>संयुक्त सचिव:</strong> सचिव अनुपस्थित हो तो उसका कार्य करेगा।<br />\r\n<strong>कोषाध्यक्ष:</strong> धनराशि का हिसाब रखेगा।</p>\r\n');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `contact`
--
ALTER TABLE `contact`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `donations`
--
ALTER TABLE `donations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `login`
--
ALTER TABLE `login`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `terms_conditions`
--
ALTER TABLE `terms_conditions`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `contact`
--
ALTER TABLE `contact`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `donations`
--
ALTER TABLE `donations`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `login`
--
ALTER TABLE `login`
  MODIFY `id` int(100) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `terms_conditions`
--
ALTER TABLE `terms_conditions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
